using System.Text.RegularExpressions;

namespace InterviewAssistant.Service.Services;

public class ConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ConfigurationService> _logger;

    public ConfigurationService(IConfiguration configuration, ILogger<ConfigurationService> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    public void ValidateConfiguration()
    {
        _logger.LogInformation("Validating service configuration");

        var errors = new List<string>();

        // Validate Azure configuration
        if (string.IsNullOrEmpty(GetConfigValue("Azure:SpeechKey")))
            errors.Add("Azure Speech Key is not configured");

        if (string.IsNullOrEmpty(GetConfigValue("Azure:SpeechRegion")))
            errors.Add("Azure Speech Region is not configured");

        // Validate Backend configuration
        if (string.IsNullOrEmpty(GetConfigValue("Backend:BaseUrl")))
            errors.Add("Backend Base URL is not configured");

        var jwtToken = GetConfigValue("Backend:JwtToken");
        var username = GetConfigValue("Backend:Username");
        var password = GetConfigValue("Backend:Password");

        if (string.IsNullOrEmpty(jwtToken) && (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password)))
            errors.Add("Either JWT Token or Username/Password must be configured for backend authentication");

        if (errors.Any())
        {
            var errorMessage = "Configuration validation failed:\n" + string.Join("\n", errors);
            _logger.LogError(errorMessage);
            throw new InvalidOperationException(errorMessage);
        }

        _logger.LogInformation("Configuration validation passed");
    }

    public string GetConfigValue(string key)
    {
        var value = _configuration[key];
        
        if (string.IsNullOrEmpty(value))
            return string.Empty;

        // Replace environment variable placeholders
        return ExpandEnvironmentVariables(value);
    }

    public T GetConfigValue<T>(string key, T defaultValue = default!)
    {
        var value = GetConfigValue(key);
        
        if (string.IsNullOrEmpty(value))
            return defaultValue;

        try
        {
            return (T)Convert.ChangeType(value, typeof(T));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to convert config value '{Key}' to type {Type}, using default value", key, typeof(T).Name);
            return defaultValue;
        }
    }

    private string ExpandEnvironmentVariables(string value)
    {
        if (string.IsNullOrEmpty(value))
            return value;

        // Replace ${VAR_NAME} patterns with environment variables
        var pattern = @"\$\{([^}]+)\}";
        return Regex.Replace(value, pattern, match =>
        {
            var envVarName = match.Groups[1].Value;
            var envValue = Environment.GetEnvironmentVariable(envVarName);
            
            if (string.IsNullOrEmpty(envValue))
            {
                _logger.LogWarning("Environment variable '{EnvVar}' is not set, keeping placeholder", envVarName);
                return match.Value; // Keep the placeholder if env var is not set
            }
            
            return envValue;
        });
    }

    public void LogConfigurationSummary()
    {
        _logger.LogInformation("Service Configuration Summary:");
        _logger.LogInformation("- Azure Speech Region: {Region}", GetConfigValue("Azure:SpeechRegion"));
        _logger.LogInformation("- Azure Speech Language: {Language}", GetConfigValue("Azure:Language"));
        _logger.LogInformation("- Backend URL: {Url}", GetConfigValue("Backend:BaseUrl"));
        _logger.LogInformation("- Audio Sample Rate: {SampleRate}Hz", GetConfigValue<int>("Audio:SampleRate"));
        _logger.LogInformation("- Audio Channels: {Channels}", GetConfigValue<int>("Audio:Channels"));
        _logger.LogInformation("- Session Auto-Generate: {AutoGenerate}", GetConfigValue<bool>("Session:AutoGenerateSessionId"));
        _logger.LogInformation("- Heartbeat Interval: {Interval}s", GetConfigValue<int>("Service:HeartbeatIntervalSeconds"));
    }
}

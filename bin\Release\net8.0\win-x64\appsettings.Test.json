{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.Hosting.Lifetime": "Information"}}, "Azure": {"SpeechKey": "YOUR_TEST_AZURE_SPEECH_KEY", "SpeechRegion": "eastus", "Language": "en-US"}, "Backend": {"BaseUrl": "https://httpbin.org", "AuthEndpoint": "/post", "TranscriptEndpoint": "/post", "Username": "test-user", "Password": "test-password", "JwtToken": ""}, "Session": {"SessionId": "test-session-123", "SpeakerId": "test-speaker", "AutoGenerateSessionId": false}, "Audio": {"SampleRate": 16000, "Channels": 1, "BitsPerSample": 16, "BufferMilliseconds": 500}, "Service": {"MaxRetryAttempts": 2, "RetryDelaySeconds": 1, "HeartbeatIntervalSeconds": 10}}
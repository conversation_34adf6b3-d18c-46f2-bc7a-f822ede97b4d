@echo off
echo === InterviewAI Service Testing ===
echo.

echo Setting environment to Development...
set DOTNET_ENVIRONMENT=Development

echo.
echo Building project...
dotnet build InterviewAssistant.Service.csproj --configuration Debug
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Running component tests...
dotnet run --project InterviewAssistant.Service.csproj test
if %ERRORLEVEL% neq 0 (
    echo Component tests failed!
    pause
    exit /b 1
)

echo.
echo Testing complete! Service is working properly.
echo.
echo To run the full service:
echo   set DOTNET_ENVIRONMENT=Development
echo   dotnet run --project InterviewAssistant.Service.csproj
echo.
echo Note: For full functionality you need:
echo   1. Valid Azure Speech Services key in appsettings.json
echo   2. Backend API endpoint configured
echo.
pause

{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.Hosting.Lifetime": "Information"}}, "Azure": {"SpeechKey": "7BYgJa31VkLQfn2DDyU1qJhzn267vcx00OtM7pbacLzjbIwhRk1KJQQJ99BGACYeBjFXJ3w3AAAYACOGVFI9", "SpeechRegion": "eastus", "Language": "en-US"}, "Backend": {"BaseUrl": "https://httpbin.org", "AuthEndpoint": "/post", "TranscriptEndpoint": "/post", "Username": "test-user", "Password": "test-password", "JwtToken": "test-token-123"}, "Session": {"SessionId": "test-session-123", "SpeakerId": "test-speaker", "AutoGenerateSessionId": false}, "Audio": {"SampleRate": 16000, "Channels": 1, "BitsPerSample": 16, "BufferMilliseconds": 500}, "Service": {"MaxRetryAttempts": 2, "RetryDelaySeconds": 1, "HeartbeatIntervalSeconds": 10}}
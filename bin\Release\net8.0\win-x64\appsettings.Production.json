{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.Hosting.Lifetime": "Warning", "Microsoft.Extensions.Hosting": "Warning"}}, "Azure": {"SpeechKey": "${AZURE_SPEECH_KEY}", "SpeechRegion": "${AZURE_SPEECH_REGION}", "Language": "en-US"}, "Backend": {"BaseUrl": "${BACKEND_BASE_URL}", "AuthEndpoint": "/api/auth/login", "TranscriptEndpoint": "/api/transcripts/stream", "Username": "${BACKEND_USERNAME}", "Password": "${BACKEND_PASSWORD}", "JwtToken": "${BACKEND_JWT_TOKEN}"}, "Session": {"SessionId": "${SESSION_ID}", "SpeakerId": "${SPEAKER_ID}", "AutoGenerateSessionId": true}, "Audio": {"SampleRate": 16000, "Channels": 1, "BitsPerSample": 16, "BufferMilliseconds": 100}, "Service": {"MaxRetryAttempts": 3, "RetryDelaySeconds": 2, "HeartbeatIntervalSeconds": 30}}
# Interview Assistant Service - Testing Guide

This guide will help you test the Interview Assistant Windows Service step by step.

## Prerequisites

1. **Windows 10/11** with microphone access
2. **.NET 8.0 SDK** installed
3. **Azure Cognitive Services Speech** subscription (for full testing)
4. **Administrator privileges** (for Windows Service installation)

## Quick Start Testing

### 1. Basic Component Testing (No Azure Required)

```powershell
# Run the automated test script
.\Test-Service.ps1 -Action test
```

This will test:
- ✅ Configuration loading
- ✅ Audio capture initializationF
- ✅ HTTP client functionality (using httpbin.org)

### 2. Console Mode Testing

```powershell
# Run service in console mode for debugging
.\Test-Service.ps1 -Action run
```

This runs the service in console mode where you can see all logs and debug output.

### 3. Full Service Testing

```powershell
# Build, test, and prepare for installation
.\Test-Service.ps1 -Action all
```

## Detailed Testing Steps

### Step 1: Configure Azure Speech Services

1. **Get Azure Speech Key:**
   - Go to [Azure Portal](https://portal.azure.com)
   - Create a "Speech Services" resource
   - Copy the Key and Region

2. **Update Configuration:**
   ```json
   // In appsettings.json or appsettings.Test.json
   {
     "Azure": {
       "SpeechKey": "7BYgJa31VkLQfn2DDyU1qJhzn267vcx00OtM7pbacLzjbIwhRk1KJQQJ99BGACYeBjFXJ3w3AAAYACOGVFI9",
       "SpeechRegion": "eastus", // or your region
       "Language": "en-US"
     }
   }
   ```

### Step 2: Configure Backend API

1. **For Testing with Mock Backend:**
   ```json
   {
     "Backend": {
       "BaseUrl": "https://httpbin.org",
       "TranscriptEndpoint": "/post",
       "JwtToken": "test-token-123"
     }
   }
   ```

2. **For Real Backend:**
   ```json
   {
     "Backend": {
       "BaseUrl": "https://your-api.com",
       "TranscriptEndpoint": "/api/transcripts/stream",
       "Username": "your-username",
       "Password": "your-password"
     }
   }
   ```

### Step 3: Test Audio Capture

```powershell
# Build the project
dotnet build

# Run in console mode
dotnet run
```

**Expected Output:**
```
[INFO] Interview Assistant Service starting
[INFO] Initializing audio capture service
[INFO] Using audio device: Microphone (Realtek Audio)
[INFO] Audio capture initialized - Sample Rate: 16000Hz, Channels: 1, Bits: 16
[INFO] Starting audio capture
[INFO] Audio capture started successfully
```

**Test:** Speak into your microphone - you should see audio data being captured.

### Step 4: Test Speech Recognition

With Azure configured, you should see:
```
[INFO] Initializing Azure Speech Recognition
[INFO] Azure Speech Recognition initialized successfully
[INFO] Starting continuous speech recognition
[DEBUG] Recognizing: Hello this is a test
[INFO] Recognized: Hello this is a test
```

### Step 5: Test Transcript Sending

Monitor the logs for HTTP requests:
```
[DEBUG] Transcript sent successfully: Hello this is a test (Final: true)
[DEBUG] Heartbeat sent successfully
```

## Windows Service Installation Testing

### Install the Service

```powershell
# Run as Administrator
.\Test-Service.ps1 -Action install
```

### Start the Service

```powershell
# Start the service
Start-Service InterviewAssistantService

# Check service status
Get-Service InterviewAssistantService

# View service logs
Get-Content "logs\interview-assistant-*.txt" -Tail 20 -Wait
```

### Stop the Service

```powershell
Stop-Service InterviewAssistantService
```

### Uninstall the Service

```powershell
.\Test-Service.ps1 -Action uninstall
```

## Troubleshooting

### Common Issues

1. **"Audio device not found"**
   - Check microphone is connected and enabled
   - Run `mmsys.cpl` to verify recording devices

2. **"Azure Speech Key invalid"**
   - Verify key and region in configuration
   - Check Azure subscription is active

3. **"Network connection failed"**
   - Check backend URL is accessible
   - Verify firewall/proxy settings

4. **"Service won't start"**
   - Check Windows Event Logs
   - Verify service has proper permissions
   - Check configuration file syntax

### Debug Commands

```powershell
# Check service logs
Get-Content "logs\interview-assistant-*.txt" -Tail 50

# Check Windows Event Logs
Get-EventLog -LogName Application -Source "Interview Assistant Service" -Newest 10

# Test network connectivity
Test-NetConnection your-backend-url.com -Port 443

# Check audio devices
Get-WmiObject -Class Win32_SoundDevice | Select-Object Name, Status
```

### Performance Testing

1. **Memory Usage:**
   ```powershell
   Get-Process "InterviewAssistant.Service" | Select-Object ProcessName, WorkingSet, CPU
   ```

2. **Audio Latency:**
   - Monitor time between speech and transcript
   - Should be < 2 seconds for real-time recognition

3. **Network Reliability:**
   - Test with intermittent network connectivity
   - Verify retry mechanisms work

## Test Scenarios

### Scenario 1: Normal Operation
1. Start service
2. Speak continuously for 5 minutes
3. Verify all transcripts are sent
4. Check for memory leaks

### Scenario 2: Network Interruption
1. Start service
2. Disconnect network
3. Speak for 30 seconds
4. Reconnect network
5. Verify transcripts are sent after reconnection

### Scenario 3: Service Recovery
1. Start service
2. Kill the process
3. Verify Windows Service Manager restarts it
4. Check service continues working

### Scenario 4: Configuration Changes
1. Update configuration file
2. Restart service
3. Verify new settings are applied

## Success Criteria

✅ **Audio Capture:** Service captures microphone audio continuously  
✅ **Speech Recognition:** Azure converts speech to text accurately  
✅ **Network Communication:** Transcripts sent to backend successfully  
✅ **Error Handling:** Service recovers from network/audio errors  
✅ **Windows Service:** Installs, starts, stops, and uninstalls properly  
✅ **Logging:** Comprehensive logs for debugging and monitoring  
✅ **Performance:** Low CPU/memory usage, real-time processing  

## Next Steps

After successful testing:
1. Deploy to production environment
2. Configure monitoring and alerting
3. Set up log aggregation
4. Create backup/recovery procedures
5. Document operational procedures

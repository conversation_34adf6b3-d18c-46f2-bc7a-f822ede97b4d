using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace InterviewAssistant.Service.Services;

public class TranscriptSender
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<TranscriptSender> _logger;
    private readonly IConfiguration _configuration;
    private string? _jwtToken;
    private DateTime _tokenExpiry;

    public TranscriptSender(HttpClient httpClient, ILogger<TranscriptSender> logger, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _logger = logger;
        _configuration = configuration;
        
        var baseUrl = _configuration["Backend:BaseUrl"];
        if (!string.IsNullOrEmpty(baseUrl))
        {
            try
            {
                _httpClient.BaseAddress = new Uri(baseUrl);
            }
            catch (UriFormatException ex)
            {
                _logger.LogError(ex, "Invalid Backend BaseUrl format: {BaseUrl}", baseUrl);
                throw new InvalidOperationException($"Invalid Backend BaseUrl format: {baseUrl}", ex);
            }
        }
    }

    public async Task SendTranscriptAsync(string sessionId, string speakerId, string text, bool isFinal)
    {
        try
        {
            await EnsureAuthenticatedAsync();

            var transcript = new TranscriptMessage
            {
                SessionId = sessionId,
                SpeakerId = speakerId,
                Text = text,
                IsFinal = isFinal,
                Timestamp = DateTime.UtcNow
            };

            var json = JsonSerializer.Serialize(transcript, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var endpoint = _configuration.GetValue<string>("Backend:TranscriptEndpoint", "/api/transcripts/stream");
            
            _logger.LogInformation("🚀 Sending transcript to {BaseUrl}{Endpoint}: {Text} (Final: {IsFinal})", 
                _httpClient.BaseAddress, endpoint, text, isFinal);
            
            var response = await _httpClient.PostAsync(endpoint, content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("✅ Transcript sent successfully: {Text} (Final: {IsFinal}) - Response: {StatusCode}", 
                    text, isFinal, response.StatusCode);
                _logger.LogDebug("📄 Response content: {ResponseContent}", responseContent);
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to send transcript. Status: {StatusCode}, Error: {Error}", 
                    response.StatusCode, errorContent);
                
                // If unauthorized, clear token to force re-authentication
                if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                {
                    _jwtToken = null;
                    _tokenExpiry = DateTime.MinValue;
                }
            }
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Network error sending transcript: {Text}", text);
            throw;
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout sending transcript: {Text}", text);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error sending transcript: {Text}", text);
            throw;
        }
    }

    private async Task EnsureAuthenticatedAsync()
    {
        if (IsTokenValid())
        {
            return;
        }

        await AuthenticateAsync();
    }

    private bool IsTokenValid()
    {
        return !string.IsNullOrEmpty(_jwtToken) && DateTime.UtcNow < _tokenExpiry.AddMinutes(-5);
    }

    private async Task AuthenticateAsync()
    {
        try
        {
            _logger.LogInformation("Authenticating with backend API");

            // Check if JWT token is provided in configuration
            var configToken = _configuration["Backend:JwtToken"];
            if (!string.IsNullOrEmpty(configToken))
            {
                _jwtToken = configToken;
                _tokenExpiry = DateTime.UtcNow.AddHours(24); // Assume token is valid for 24 hours
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _jwtToken);
                _logger.LogInformation("Using JWT token from configuration");
                return;
            }

            // Otherwise, authenticate with username/password
            var username = _configuration["Backend:Username"];
            var password = _configuration["Backend:Password"];
            var authEndpoint = _configuration.GetValue<string>("Backend:AuthEndpoint", "/api/auth/login");

            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                throw new InvalidOperationException("Backend authentication credentials not configured");
            }

            var loginRequest = new LoginRequest
            {
                Username = username,
                Password = password
            };

            var json = JsonSerializer.Serialize(loginRequest, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(authEndpoint, content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var loginResponse = JsonSerializer.Deserialize<LoginResponse>(responseContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                if (loginResponse?.Token != null)
                {
                    _jwtToken = loginResponse.Token;
                    _tokenExpiry = loginResponse.ExpiresAt ?? DateTime.UtcNow.AddHours(1);
                    _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _jwtToken);
                    
                    _logger.LogInformation("Successfully authenticated with backend API");
                }
                else
                {
                    throw new InvalidOperationException("Invalid authentication response");
                }
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new InvalidOperationException($"Authentication failed: {response.StatusCode} - {errorContent}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to authenticate with backend API");
            throw;
        }
    }

    public async Task SendHeartbeatAsync()
    {
        try
        {
            await EnsureAuthenticatedAsync();
            
            var heartbeat = new HeartbeatMessage
            {
                Timestamp = DateTime.UtcNow,
                ServiceVersion = "1.0.0",
                Status = "active"
            };

            var json = JsonSerializer.Serialize(heartbeat, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            _logger.LogInformation("💓 Sending heartbeat to {BaseUrl}/api/heartbeat", _httpClient.BaseAddress);
            
            var response = await _httpClient.PostAsync("/api/heartbeat", content);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("💚 Heartbeat sent successfully - Response: {StatusCode}", response.StatusCode);
            }
            else
            {
                _logger.LogWarning("💔 Failed to send heartbeat. Status: {StatusCode}", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending heartbeat");
        }
    }
}

public class TranscriptMessage
{
    public string SessionId { get; set; } = string.Empty;
    public string SpeakerId { get; set; } = string.Empty;
    public string Text { get; set; } = string.Empty;
    public bool IsFinal { get; set; }
    public DateTime Timestamp { get; set; }
}

public class LoginRequest
{
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
}

public class LoginResponse
{
    public string? Token { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string? RefreshToken { get; set; }
}

public class HeartbeatMessage
{
    public DateTime Timestamp { get; set; }
    public string ServiceVersion { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
}

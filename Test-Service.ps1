# Interview Assistant Service Test Script
param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("build", "test", "run", "install", "uninstall", "all")]
    [string]$Action = "all",
    
    [Parameter(Mandatory=$false)]
    [string]$ServiceName = "InterviewAssistantService",
    
    [Parameter(Mandatory=$false)]
    [string]$InstallPath = "C:\Services\InterviewAssistant"
)

Write-Host "=== Interview Assistant Service Test Script ===" -ForegroundColor Green
Write-Host "Action: $Action" -ForegroundColor Yellow

function Test-Prerequisites {
    Write-Host "`nChecking prerequisites..." -ForegroundColor Cyan
    
    # Check if .NET is installed
    try {
        $dotnetVersion = dotnet --version
        Write-Host "✓ .NET SDK Version: $dotnetVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ .NET SDK not found. Please install .NET 8.0 SDK" -ForegroundColor Red
        exit 1
    }
    
    # Check if running as administrator for service operations
    if ($Action -in @("install", "uninstall")) {
        $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
        if (-not $isAdmin) {
            Write-Host "✗ Administrator privileges required for service installation" -ForegroundColor Red
            Write-Host "Please run PowerShell as Administrator" -ForegroundColor Yellow
            exit 1
        }
        Write-Host "✓ Running with Administrator privileges" -ForegroundColor Green
    }
}

function Build-Service {
    Write-Host "`nBuilding service..." -ForegroundColor Cyan
    
    try {
        # Restore packages
        Write-Host "Restoring NuGet packages..."
        dotnet restore
        
        # Build the project
        Write-Host "Building project..."
        dotnet build --configuration Release
        
        Write-Host "✓ Service built successfully" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "✗ Build failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-ServiceComponents {
    Write-Host "`nTesting service components..." -ForegroundColor Cyan
    
    try {
        # Compile and run test program
        Write-Host "Compiling test program..."
        dotnet build --configuration Debug
        
        Write-Host "Running component tests..."
        Write-Host "Note: You'll need to configure Azure Speech Key in appsettings.Test.json for full testing" -ForegroundColor Yellow
        
        # Run the test program
        dotnet run --project . --configuration Debug TestProgram.cs
        
        Write-Host "✓ Component tests completed" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "✗ Component tests failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Run-ServiceConsole {
    Write-Host "`nRunning service in console mode..." -ForegroundColor Cyan
    Write-Host "Press Ctrl+C to stop the service" -ForegroundColor Yellow
    
    try {
        # Set environment to Development for console testing
        $env:DOTNET_ENVIRONMENT = "Development"
        dotnet run --configuration Debug
    }
    catch {
        Write-Host "✗ Service run failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Publish-Service {
    Write-Host "`nPublishing service for deployment..." -ForegroundColor Cyan
    
    try {
        # Publish as self-contained executable
        dotnet publish -c Release -r win-x64 --self-contained true -o "publish"
        
        Write-Host "✓ Service published to 'publish' folder" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "✗ Publish failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Install-WindowsService {
    Write-Host "`nInstalling Windows Service..." -ForegroundColor Cyan
    
    try {
        # Create installation directory
        if (-not (Test-Path $InstallPath)) {
            New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
            Write-Host "✓ Created installation directory: $InstallPath" -ForegroundColor Green
        }
        
        # Copy published files
        if (Test-Path "publish") {
            Copy-Item -Path "publish\*" -Destination $InstallPath -Recurse -Force
            Write-Host "✓ Copied service files to installation directory" -ForegroundColor Green
        }
        else {
            Write-Host "✗ Published files not found. Run with -Action build first" -ForegroundColor Red
            return $false
        }
        
        # Install the service
        $servicePath = Join-Path $InstallPath "InterviewAssistant.Service.exe"
        
        # Remove existing service if it exists
        $existingService = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
        if ($existingService) {
            Write-Host "Removing existing service..." -ForegroundColor Yellow
            Stop-Service -Name $ServiceName -Force -ErrorAction SilentlyContinue
            sc.exe delete $ServiceName
            Start-Sleep -Seconds 2
        }
        
        # Create new service
        New-Service -Name $ServiceName `
                   -BinaryPathName $servicePath `
                   -DisplayName "Interview Assistant Service" `
                   -Description "Captures audio and sends transcripts to AI backend for interview assistance" `
                   -StartupType Manual
        
        Write-Host "✓ Windows Service installed successfully" -ForegroundColor Green
        Write-Host "Service Name: $ServiceName" -ForegroundColor Cyan
        Write-Host "Service Path: $servicePath" -ForegroundColor Cyan
        Write-Host "Use 'Start-Service $ServiceName' to start the service" -ForegroundColor Yellow
        
        return $true
    }
    catch {
        Write-Host "✗ Service installation failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Uninstall-WindowsService {
    Write-Host "`nUninstalling Windows Service..." -ForegroundColor Cyan
    
    try {
        $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
        if ($service) {
            # Stop the service if running
            if ($service.Status -eq 'Running') {
                Write-Host "Stopping service..." -ForegroundColor Yellow
                Stop-Service -Name $ServiceName -Force
            }
            
            # Remove the service
            sc.exe delete $ServiceName
            Write-Host "✓ Service uninstalled successfully" -ForegroundColor Green
        }
        else {
            Write-Host "Service not found" -ForegroundColor Yellow
        }
        
        # Optionally remove installation directory
        if (Test-Path $InstallPath) {
            $response = Read-Host "Remove installation directory '$InstallPath'? (y/N)"
            if ($response -eq 'y' -or $response -eq 'Y') {
                Remove-Item -Path $InstallPath -Recurse -Force
                Write-Host "✓ Installation directory removed" -ForegroundColor Green
            }
        }
        
        return $true
    }
    catch {
        Write-Host "✗ Service uninstallation failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main execution
Test-Prerequisites

switch ($Action) {
    "build" {
        Build-Service
        Publish-Service
    }
    "test" {
        if (Build-Service) {
            Test-ServiceComponents
        }
    }
    "run" {
        if (Build-Service) {
            Run-ServiceConsole
        }
    }
    "install" {
        if (Build-Service -and (Publish-Service)) {
            Install-WindowsService
        }
    }
    "uninstall" {
        Uninstall-WindowsService
    }
    "all" {
        if (Build-Service) {
            Test-ServiceComponents
            Write-Host "`nTo install as Windows Service, run:" -ForegroundColor Cyan
            Write-Host ".\Test-Service.ps1 -Action install" -ForegroundColor White
        }
    }
}

Write-Host "`n=== Script completed ===" -ForegroundColor Green

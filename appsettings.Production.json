{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.Extensions.Hosting": "Information", "System.Net.Http.HttpClient": "Information"}}, "Azure": {"SpeechKey": "dummy-azure-key-not-configured", "SpeechRegion": "eastus", "Language": "en-US"}, "Backend": {"BaseUrl": "https://httpbin.org", "AuthEndpoint": "/post", "TranscriptEndpoint": "/post", "Username": "dummy-user", "Password": "dummy-password", "JwtToken": "dummy-jwt-token-for-testing"}, "Session": {"SessionId": "demo-session-123", "SpeakerId": "demo-speaker", "AutoGenerateSessionId": false}, "Audio": {"SampleRate": 16000, "Channels": 1, "BitsPerSample": 16, "BufferMilliseconds": 100}, "Service": {"MaxRetryAttempts": 3, "RetryDelaySeconds": 2, "HeartbeatIntervalSeconds": 30}}
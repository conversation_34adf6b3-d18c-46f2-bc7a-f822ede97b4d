using Microsoft.CognitiveServices.Speech;
using Microsoft.CognitiveServices.Speech.Audio;

namespace InterviewAssistant.Service.Services;

public class AzureSpeechRecognizer : IDisposable
{
    private readonly ILogger<AzureSpeechRecognizer> _logger;
    private readonly IConfiguration _configuration;
    private readonly TranscriptSender _transcriptSender;
    private readonly AudioCaptureService _audioCaptureService;
    
    private SpeechConfig? _speechConfig;
    private SpeechRecognizer? _recognizer;
    private PushAudioInputStream? _pushStream;
    private AudioConfig? _audioConfig;
    private bool _isRecognizing;

    public AzureSpeechRecognizer(
        ILogger<AzureSpeechRecognizer> logger,
        IConfiguration configuration,
        TranscriptSender transcriptSender,
        AudioCaptureService audioCaptureService)
    {
        _logger = logger;
        _configuration = configuration;
        _transcriptSender = transcriptSender;
        _audioCaptureService = audioCaptureService;
    }

    public async Task InitializeAsync()
    {
        try
        {
            _logger.LogInformation("Initializing Azure Speech Recognition");

            var speechKey = _configuration["Azure:SpeechKey"];
            var speechRegion = _configuration["Azure:SpeechRegion"];
            var language = _configuration.GetValue<string>("Azure:Language", "en-US");

            if (string.IsNullOrEmpty(speechKey) || string.IsNullOrEmpty(speechRegion) || 
                speechKey.StartsWith("dummy") || speechKey.Contains("YOUR_"))
            {
                _logger.LogWarning("⚠️ Azure Speech Services not configured properly - service will run in audio-only mode");
                _logger.LogInformation("💡 To enable speech recognition, configure valid Azure Speech Key and Region");
                throw new InvalidOperationException("Azure Speech Key and Region must be configured for speech recognition");
            }

            // Create speech configuration
            _speechConfig = SpeechConfig.FromSubscription(speechKey, speechRegion);
            _speechConfig.SpeechRecognitionLanguage = language;
            _speechConfig.EnableDictation();

            // Create push audio input stream
            var audioFormat = AudioStreamFormat.GetWaveFormatPCM(
                samplesPerSecond: (uint)_configuration.GetValue<int>("Audio:SampleRate", 16000),
                bitsPerSample: (byte)_configuration.GetValue<int>("Audio:BitsPerSample", 16),
                channels: (byte)_configuration.GetValue<int>("Audio:Channels", 1));

            _pushStream = AudioInputStream.CreatePushStream(audioFormat);
            _audioConfig = AudioConfig.FromStreamInput(_pushStream);

            // Create speech recognizer
            _recognizer = new SpeechRecognizer(_speechConfig, _audioConfig);

            // Subscribe to events
            _recognizer.Recognizing += OnRecognizing;
            _recognizer.Recognized += OnRecognized;
            _recognizer.Canceled += OnCanceled;
            _recognizer.SessionStarted += OnSessionStarted;
            _recognizer.SessionStopped += OnSessionStopped;

            // Subscribe to audio capture events
            _audioCaptureService.AudioDataAvailable += OnAudioDataAvailable;

            _logger.LogInformation("Azure Speech Recognition initialized successfully");
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize Azure Speech Recognition");
            throw;
        }
    }

    public async Task StartRecognitionAsync()
    {
        try
        {
            if (_recognizer == null)
            {
                throw new InvalidOperationException("Speech recognizer not initialized. Call InitializeAsync first.");
            }

            _logger.LogInformation("Starting continuous speech recognition");
            
            _isRecognizing = true;
            await _recognizer.StartContinuousRecognitionAsync();

            _logger.LogInformation("Continuous speech recognition started");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start speech recognition");
            throw;
        }
    }

    public async Task StopRecognitionAsync()
    {
        try
        {
            if (_recognizer != null && _isRecognizing)
            {
                _logger.LogInformation("Stopping speech recognition");
                
                _isRecognizing = false;
                await _recognizer.StopContinuousRecognitionAsync();
                
                _pushStream?.Close();
                
                _logger.LogInformation("Speech recognition stopped");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping speech recognition");
        }
    }

    private void OnAudioDataAvailable(object? sender, AudioDataEventArgs e)
    {
        try
        {
            if (_isRecognizing && _pushStream != null)
            {
                _pushStream.Write(e.AudioData);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error writing audio data to push stream");
        }
    }

    private async void OnRecognizing(object? sender, SpeechRecognitionEventArgs e)
    {
        try
        {
            if (!string.IsNullOrWhiteSpace(e.Result.Text))
            {
                _logger.LogDebug("Recognizing: {Text}", e.Result.Text);
                
                var sessionId = GetSessionId();
                var speakerId = _configuration.GetValue<string>("Session:SpeakerId", "user") ?? "user";
                
                await _transcriptSender.SendTranscriptAsync(sessionId, speakerId, e.Result.Text, false);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing recognizing event");
        }
    }

    private async void OnRecognized(object? sender, SpeechRecognitionEventArgs e)
    {
        try
        {
            if (e.Result.Reason == ResultReason.RecognizedSpeech && !string.IsNullOrWhiteSpace(e.Result.Text))
            {
                _logger.LogInformation("Recognized: {Text}", e.Result.Text);
                
                var sessionId = GetSessionId();
                var speakerId = _configuration.GetValue<string>("Session:SpeakerId", "user") ?? "user";
                
                await _transcriptSender.SendTranscriptAsync(sessionId, speakerId, e.Result.Text, true);
            }
            else if (e.Result.Reason == ResultReason.NoMatch)
            {
                _logger.LogDebug("No speech could be recognized");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing recognized event");
        }
    }

    private void OnCanceled(object? sender, SpeechRecognitionCanceledEventArgs e)
    {
        _logger.LogWarning("Speech recognition canceled: {Reason}, Error: {ErrorDetails}", 
            e.Reason, e.ErrorDetails);

        if (e.Reason == CancellationReason.Error)
        {
            _logger.LogError("Speech recognition error: {ErrorCode} - {ErrorDetails}", 
                e.ErrorCode, e.ErrorDetails);
        }
    }

    private void OnSessionStarted(object? sender, SessionEventArgs e)
    {
        _logger.LogInformation("Speech recognition session started: {SessionId}", e.SessionId);
    }

    private void OnSessionStopped(object? sender, SessionEventArgs e)
    {
        _logger.LogInformation("Speech recognition session stopped: {SessionId}", e.SessionId);
    }

    private string GetSessionId()
    {
        var sessionId = _configuration["Session:SessionId"];
        
        if (string.IsNullOrEmpty(sessionId) && _configuration.GetValue<bool>("Session:AutoGenerateSessionId", true))
        {
            sessionId = Guid.NewGuid().ToString();
            _logger.LogInformation("Generated new session ID: {SessionId}", sessionId);
        }

        return sessionId ?? "default-session";
    }

    public void Dispose()
    {
        _recognizer?.Dispose();
        _audioConfig?.Dispose();
        _pushStream?.Dispose();
        // _speechConfig does not implement IDisposable, so do not call Dispose on it
    }
}

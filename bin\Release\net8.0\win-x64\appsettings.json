{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.Hosting.Lifetime": "Information"}}, "Azure": {"SpeechKey": "YOUR_AZURE_SPEECH_KEY", "SpeechRegion": "eastus", "Language": "en-US"}, "Backend": {"BaseUrl": "https://your-api-backend.com", "AuthEndpoint": "/api/auth/login", "TranscriptEndpoint": "/api/transcripts/stream", "Username": "your-username", "Password": "your-password", "JwtToken": ""}, "Session": {"SessionId": "", "SpeakerId": "user", "AutoGenerateSessionId": true}, "Audio": {"SampleRate": 16000, "Channels": 1, "BitsPerSample": 16, "BufferMilliseconds": 100}, "Service": {"MaxRetryAttempts": 3, "RetryDelaySeconds": 2, "HeartbeatIntervalSeconds": 30}}
using InterviewAssistant.Service.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;

namespace InterviewAssistant.Service.Test;

public class TestProgram
{
    public static async Task Main(string[] args)
    {
        // Configure Serilog for testing
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File("logs/test-.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        try
        {
            Console.WriteLine("=== Interview Assistant Service Test ===");
            Console.WriteLine("This will test the service components individually.");
            Console.WriteLine("Starting tests automatically...\n");

            // Setup DI container
            var services = new ServiceCollection();
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.Test.json", optional: false)
                .Build();

            services.AddSingleton<IConfiguration>(configuration);
            services.AddLogging(builder => builder.AddSerilog());
            services.AddHttpClient<TranscriptSender>();
            services.AddSingleton<ConfigurationService>();
            services.AddSingleton<AudioCaptureService>();
            services.AddSingleton<TranscriptSender>();

            var serviceProvider = services.BuildServiceProvider();

            // Test 1: Configuration Service
            await TestConfigurationService(serviceProvider);

            // Test 2: Audio Capture Service (without Azure dependency)
            await TestAudioCaptureService(serviceProvider);

            // Test 3: Transcript Sender (using httpbin.org as mock backend)
            await TestTranscriptSender(serviceProvider);

            Console.WriteLine("\n=== All Tests Completed ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Test failed with error: {ex.Message}");
            Log.Fatal(ex, "Test application terminated unexpectedly");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static async Task TestConfigurationService(ServiceProvider serviceProvider)
    {
        Console.WriteLine("\n--- Testing Configuration Service ---");
        
        try
        {
            var configService = serviceProvider.GetRequiredService<ConfigurationService>();
            
            // Test configuration reading
            var azureRegion = configService.GetConfigValue("Azure:SpeechRegion");
            var backendUrl = configService.GetConfigValue("Backend:BaseUrl");
            var sampleRate = configService.GetConfigValue<int>("Audio:SampleRate");
            
            Console.WriteLine($"✓ Azure Region: {azureRegion}");
            Console.WriteLine($"✓ Backend URL: {backendUrl}");
            Console.WriteLine($"✓ Sample Rate: {sampleRate}Hz");
            
            configService.LogConfigurationSummary();
            
            Console.WriteLine("✓ Configuration Service: PASSED");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Configuration Service: FAILED - {ex.Message}");
        }
    }

    private static async Task TestAudioCaptureService(ServiceProvider serviceProvider)
    {
        Console.WriteLine("\n--- Testing Audio Capture Service ---");
        
        try
        {
            var audioService = serviceProvider.GetRequiredService<AudioCaptureService>();
            
            Console.WriteLine("Initializing audio capture...");
            await audioService.InitializeAsync();
            Console.WriteLine("✓ Audio capture initialized");

            // Subscribe to audio events
            var audioDataReceived = false;
            audioService.AudioDataAvailable += (sender, args) =>
            {
                audioDataReceived = true;
                Console.WriteLine($"✓ Audio data received: {args.AudioData.Length} bytes at {args.Timestamp}");
            };

            Console.WriteLine("Starting audio capture for 2 seconds...");
            Console.WriteLine("Testing audio initialization (no actual capture needed for this test)...");
            
            await audioService.StartCaptureAsync();
            await Task.Delay(2000); // Capture for 2 seconds
            await audioService.StopCaptureAsync();

            if (audioDataReceived)
            {
                Console.WriteLine("✓ Audio Capture Service: PASSED");
            }
            else
            {
                Console.WriteLine("⚠ Audio Capture Service: No audio data received (check microphone)");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Audio Capture Service: FAILED - {ex.Message}");
        }
    }

    private static async Task TestTranscriptSender(ServiceProvider serviceProvider)
    {
        Console.WriteLine("\n--- Testing Transcript Sender ---");
        
        try
        {
            var transcriptSender = serviceProvider.GetRequiredService<TranscriptSender>();
            
            Console.WriteLine("Sending test transcript...");
            await transcriptSender.SendTranscriptAsync(
                sessionId: "test-session-123",
                speakerId: "test-speaker",
                text: "This is a test transcript message",
                isFinal: true
            );
            
            Console.WriteLine("✓ Test transcript sent successfully");
            
            Console.WriteLine("Sending heartbeat...");
            await transcriptSender.SendHeartbeatAsync();
            Console.WriteLine("✓ Heartbeat sent successfully");
            
            Console.WriteLine("✓ Transcript Sender: PASSED");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Transcript Sender: FAILED - {ex.Message}");
        }
    }
}

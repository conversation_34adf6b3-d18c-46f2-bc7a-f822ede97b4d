using InterviewAssistant.Service;
using InterviewAssistant.Service.Services;
using Serilog;
using Polly;
using Polly.Extensions.Http;

using System.Net.Http;

// Check if we're running in test mode
if (args.Length > 0 && args[0] == "test")
{
    await InterviewAssistant.Service.Test.TestProgram.Main(args.Skip(1).ToArray());
    return;
}

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/interview-assistant-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

try
{
    Log.Information("Starting Interview Assistant Service");

    var builder = Host.CreateApplicationBuilder(args);

    // Add Serilog
    builder.Services.AddSerilog();

    // Configure Windows Service
    builder.Services.AddWindowsService(options =>
    {
        options.ServiceName = "Interview Assistant Service";
    });

    // Add HTTP client with Polly retry policy (using custom DelegatingHandler for Polly 3.0.0)
    builder.Services.AddHttpClient<TranscriptSender>(client =>
    {
        client.Timeout = TimeSpan.FromSeconds(30);
    })
    .AddHttpMessageHandler(() => new RetryPolicyHandler(GetRetryPolicy()));

    // Register services
    builder.Services.AddSingleton<ConfigurationService>();
    builder.Services.AddSingleton<AudioCaptureService>();
    builder.Services.AddSingleton<AzureSpeechRecognizer>();
    builder.Services.AddSingleton<TranscriptSender>();
    builder.Services.AddHostedService<Worker>();

    var host = builder.Build();
    await host.RunAsync();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}

static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
{
    return HttpPolicyExtensions
        .HandleTransientHttpError()
        .WaitAndRetryAsync(
            retryCount: 3,
            sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
            onRetry: (outcome, timespan, retryCount, context) =>
            {
                Log.Warning("Retry {RetryCount} for {OperationKey} in {Delay}ms", 
                    retryCount, context.OperationKey, timespan.TotalMilliseconds);
            });
}

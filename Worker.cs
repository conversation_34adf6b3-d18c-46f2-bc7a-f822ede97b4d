using InterviewAssistant.Service.Services;

namespace InterviewAssistant.Service;

public class Worker : BackgroundService
{
    private readonly ILogger<Worker> _logger;
    private readonly AudioCaptureService _audioCaptureService;
    private readonly AzureSpeechRecognizer _speechRecognizer;
    private readonly IConfiguration _configuration;

    public Worker(
        ILogger<Worker> logger,
        AudioCaptureService audioCaptureService,
        AzureSpeechRecognizer speechRecognizer,
        IConfiguration configuration)
    {
        _logger = logger;
        _audioCaptureService = audioCaptureService;
        _speechRecognizer = speechRecognizer;
        _configuration = configuration;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            _logger.LogInformation("Interview Assistant Service starting at: {time}", DateTimeOffset.Now);

            // Initialize services
            var speechRecognitionAvailable = false;
            try
            {
                await _speechRecognizer.InitializeAsync();
                speechRecognitionAvailable = true;
                _logger.LogInformation("🎙️ Speech recognition initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️ Speech recognition not available, continuing in audio-only mode");
            }

            await _audioCaptureService.InitializeAsync();

            // Start audio capture
            await _audioCaptureService.StartCaptureAsync();
            
            // Start speech recognition if available
            if (speechRecognitionAvailable)
            {
                await _speechRecognizer.StartRecognitionAsync();
                _logger.LogInformation("🎯 Service started with full speech recognition");
            }
            else
            {
                _logger.LogInformation("🔊 Service started in audio-only mode (no speech recognition)");
                _logger.LogInformation("📝 Audio will be captured but not transcribed");
                
                _logger.LogInformation("🎬 Demo mode: Audio will be captured but speech recognition is disabled");
                _logger.LogInformation("💡 Configure Azure Speech Key to enable full functionality");
            }

            _logger.LogInformation("Interview Assistant Service started successfully");

            // Keep the service running
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(1000, stoppingToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred in Interview Assistant Service");
            throw;
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Interview Assistant Service stopping");

        try
        {
            await _speechRecognizer.StopRecognitionAsync();
            await _audioCaptureService.StopCaptureAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while stopping services");
        }

        await base.StopAsync(cancellationToken);
        _logger.LogInformation("Interview Assistant Service stopped");
    }
}
